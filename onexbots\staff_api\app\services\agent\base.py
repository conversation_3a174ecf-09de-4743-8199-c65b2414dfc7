from typing import Dict, Any, List, Optional, TypedDict, Annotated, AsyncGenerator
from langchain_core.messages import (
    BaseMessage,
    convert_to_openai_messages,
    HumanMessage,
    AIMessage,
)
from enum import Enum
from langchain_core.tools import BaseTool
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg_pool import AsyncConnectionPool
import logging
from langgraph.types import RunnableConfig
from langgraph.graph.state import (
    CompiledStateGraph,
)
from onexbots.shared.config import settings, Environment
from onexbots.shared.services.virtual_staff_service import (
    VirtualStaff,
)
from .agent_builder import build_dynamic_react_agent


# Configure logger
logger = logging.getLogger(__name__)


class LanggraphNode(Enum):
    AGENT = "agent"
    TOOLS = "tools"
    PRE_MODEL_HOOK = "pre_model_hook"


class AgentState(TypedDict):
    """State for the agent."""

    messages: List[BaseMessage]
    context: str
    tools: List[BaseTool]
    current_tool: Optional[str]
    tool_input: Optional[str]
    tool_output: Optional[str]
    current_agent: str  # Track which agent is currently active


class BaseAgent:
    """Base agent class using LangGraph."""

    _connection_pool: AsyncConnectionPool = None

    def __init__(self):
        """Initialize the agent with configuration."""
        self._connection_pool: Optional[AsyncConnectionPool] = None

    async def _get_connection_pool(self) -> Optional[AsyncConnectionPool]:
        """Get or create a PostgreSQL connection pool configured for DictCursor."""
        if self._connection_pool is None:
            if not settings.POSTGRES_URL:
                logger.warning(
                    "POSTGRES_URL not set, cannot create connection pool. Checkpointer disabled."
                )
                return None
            try:
                max_size = settings.POSTGRES_POOL_SIZE
                logger.info(f"Creating connection pool with max_size: {max_size}")
                logger.info(f"POSTGRES_URL: {settings.POSTGRES_URL}")
                self._connection_pool = AsyncConnectionPool(
                    settings.POSTGRES_URL,
                    open=False,
                    max_size=max_size,
                    min_size=1,
                    kwargs={
                        "autocommit": True,
                        "connect_timeout": 5,
                        "prepare_threshold": None,
                    },
                )
                await self._connection_pool.open()
                logger.info("PostgreSQL connection pool created with DictCursor")
            except ImportError:
                logger.error(
                    "psycopg or psycopg[pool] not installed. Cannot create connection pool."
                )
                if settings.ENVIRONMENT == Environment.DEVELOPMENT:
                    return None
                raise
            except Exception as e:
                logger.error(
                    f"PostgreSQL connection pool creation failed{str(e)}",
                    environment=settings.ENVIRONMENT.value,
                )
                if settings.ENVIRONMENT == Environment.PRODUCTION:
                    logger.warning(
                        "Continuing without connection pool (checkpointer disabled)",
                    )
                    return None
                raise e
        return self._connection_pool

    async def _get_compiled_graph(self, staff_id: str) -> CompiledStateGraph:
        """Build the LangGraph workflow using supervisor pattern."""
        logger.info("Get Compiled Graph function called")

        connection_pool = await self._get_connection_pool()

        checkpoint_saver = None
        if connection_pool:
            checkpoint_saver = AsyncPostgresSaver(connection_pool)
            await checkpoint_saver.setup()

        # Compile the supervisor graph
        react_agent: CompiledStateGraph = await build_dynamic_react_agent(staff_id)
        react_agent.checkpointer = checkpoint_saver

        return react_agent

    async def get_response(
        self,
        staff: VirtualStaff,
        message: str,
        conversation_id: str,
    ) -> str:
        """Get a response from the LLM using the compiled graph."""
        compiled_graph = await self._get_compiled_graph(staff.get("id"))

        staff_id = staff.get("id")
        config: RunnableConfig = {
            "configurable": {
                "thread_id": conversation_id,
                "company_id": staff.get("company_id"),
            }
        }
        logger.debug(
            "Invoking graph for response",
            staff_id,
        )
        try:
            # Convert Message to HumanMessage and include state
            input_data = {"messages": [HumanMessage(content=message)]}
            response = await compiled_graph.ainvoke(input_data, config)
            if isinstance(response, dict) and "messages" in response:
                processed_messages = self.__process_messages(response["messages"])
                logger.debug(
                    "Graph invocation successful",
                    staff_id,
                    message_count=len(processed_messages),
                )
                # Return the last message content
                if processed_messages:
                    return processed_messages[-1]
                return ""
            else:
                logger.error(
                    "Unexpected response structure from graph",
                    staff_id,
                    response=response,
                )
                raise Exception("Invalid response structure received from graph")
        except Exception as e:
            logger.error("Error during graph invocation", staff_id)
            raise e

    async def get_stream_response(
        self, staff: VirtualStaff, message: str, conversation_id: str
    ) -> AsyncGenerator[str, None]:
        """Get a stream response from the LLM using the compiled graph."""
        compiled_graph = await self._get_compiled_graph(staff.get("id"))
        staff_id = staff.get("id")
        config: RunnableConfig = {
            "configurable": {
                "thread_id": conversation_id,
                "company_id": staff.get("company_id"),
            }
        }
        logger.debug("Streaming graph response", staff_id)

        try:
            # Convert Message to HumanMessage and include state
            input_data = {"messages": [HumanMessage(content=message)]}
            async for msg, metadata in compiled_graph.astream(
                input_data, config, stream_mode="messages"
            ):
                if metadata.get("langgraph_node") == LanggraphNode.AGENT.value:
                    if isinstance(msg, AIMessage):
                        content = msg.content or ""
                        has_tool_calls = hasattr(msg, "tool_calls") and len(
                            msg.tool_calls
                        )
                        if content and not has_tool_calls:
                            yield content

        except Exception as stream_error:
            logger.error("Error during graph stream", staff_id, error=str(stream_error))
            yield {"content": f"Error: {str(stream_error)}", "is_last": True}
        finally:
            logger.debug("Graph stream finished", staff_id)

    def __process_messages(self, messages: list[BaseMessage]) -> list[str]:
        """Process messages and return only their content."""
        openai_style_messages = convert_to_openai_messages(messages)
        return [
            message["content"]
            for message in openai_style_messages
            if message["role"] in ["assistant", "user"] and message["content"]
        ]

    async def cleanup(self):
        """Cleanup resources."""
        if self._connection_pool:
            await self._connection_pool.close()
            self._connection_pool = None
