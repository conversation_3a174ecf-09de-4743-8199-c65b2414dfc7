from fastapi import APIRouter, Body, Path, status
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from app.services.prompts.langfuse_prompt_service import LangfusePromptService

router = APIRouter(
    prefix="/langfuse",
    tags=["langfuse"],
    responses={status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal Server Error"}},
)

class PromptExperimentRequest(BaseModel):
    dataset_name: str = Field(..., description="Name of the Langfuse dataset to use")
    run_name: str = Field(..., description="Name for this experiment run")

class PromptExperimentResponse(BaseModel):
    success: bool
    message: str

class CreateDatasetRequest(BaseModel):
    dataset_name: str = Field(..., description="Name of the dataset to create")
    description: Optional[str] = Field("", description="Description of the dataset")

class CreateDatasetResponse(BaseModel):
    success: bool
    message: str

class AddDatasetItemsRequest(BaseModel):
    dataset_name: str = Field(..., description="Name of the dataset to add items to")
    items: List[Dict[str, Any]] = Field(
        ...,
        description="List of items to add (each with input, expected_output, and optional metadata)",
    )

class AddDatasetItemsResponse(BaseModel):
    success: bool
    message: str

dataset_name = "general_qa_dataset"

# Dataset items - 10 diverse examples
dataset_items = [
    {
        "input": {
            "question": "What is the capital of France?",
            "context": "Geography question",
        },
        "expected_output": "Paris",
        "metadata": {"difficulty": "easy", "category": "geography", "type": "factual"},
    },
    {
        "input": {
            "question": "Explain the difference between machine learning and artificial intelligence.",
            "context": "Technology concept explanation",
        },
        "expected_output": "Artificial Intelligence (AI) is a broad field focused on creating systems that can perform tasks requiring human-like intelligence. Machine Learning (ML) is a subset of AI that focuses on algorithms that can learn and improve from data without being explicitly programmed for every scenario.",
        "metadata": {
            "difficulty": "medium",
            "category": "technology",
            "type": "conceptual",
        },
    },
    {
        "input": {
            "question": "Write a haiku about rain.",
            "context": "Creative writing task",
        },
        "expected_output": "Gentle drops descend\nWashing the earth clean and fresh\nLife awakens new",
        "metadata": {
            "difficulty": "medium",
            "category": "creative",
            "type": "generation",
        },
    },
    {
        "input": {
            "question": "What is 15% of 240?",
            "context": "Basic math calculation",
        },
        "expected_output": "36",
        "metadata": {"difficulty": "easy", "category": "math", "type": "calculation"},
    },
    {
        "input": {
            "question": "Summarize the main causes of World War I in 2-3 sentences.",
            "context": "Historical summary task",
        },
        "expected_output": "World War I was primarily caused by a complex web of militarism, alliance systems, imperialism, and nationalism in Europe. The immediate trigger was the assassination of Archduke Franz Ferdinand of Austria-Hungary in 1914, which activated the alliance system and led to widespread conflict.",
        "metadata": {
            "difficulty": "medium",
            "category": "history",
            "type": "summarization",
        },
    },
    {
        "input": {
            "question": "You have a 3-gallon jug and a 5-gallon jug. How can you measure exactly 4 gallons of water?",
            "context": "Logic puzzle",
        },
        "expected_output": "1. Fill the 5-gallon jug completely. 2. Pour from the 5-gallon jug into the 3-gallon jug (leaving 2 gallons in the 5-gallon jug). 3. Empty the 3-gallon jug. 4. Pour the 2 gallons from the 5-gallon jug into the 3-gallon jug. 5. Fill the 5-gallon jug completely again. 6. Pour from the 5-gallon jug into the 3-gallon jug until it's full (this adds 1 gallon, leaving exactly 4 gallons in the 5-gallon jug).",
        "metadata": {
            "difficulty": "hard",
            "category": "logic",
            "type": "problem_solving",
        },
    },
    {
        "input": {
            "question": "What does the idiom 'break the ice' mean?",
            "context": "Language and idioms",
        },
        "expected_output": "To 'break the ice' means to initiate conversation or interaction in a social situation, especially to help people feel more comfortable and reduce tension or awkwardness.",
        "metadata": {
            "difficulty": "easy",
            "category": "language",
            "type": "definition",
        },
    },
    {
        "input": {
            "question": "List three benefits of regular exercise.",
            "context": "Health and wellness",
        },
        "expected_output": "1. Improves cardiovascular health and reduces risk of heart disease. 2. Strengthens muscles and bones, reducing risk of osteoporosis. 3. Enhances mental health by reducing stress, anxiety, and symptoms of depression.",
        "metadata": {"difficulty": "easy", "category": "health", "type": "listing"},
    },
    {
        "input": {
            "question": "If a train travels at 60 mph for 2.5 hours, how far does it travel?",
            "context": "Distance calculation word problem",
        },
        "expected_output": "150 miles (Distance = Speed × Time = 60 mph × 2.5 hours = 150 miles)",
        "metadata": {"difficulty": "easy", "category": "math", "type": "word_problem"},
    },
    {
        "input": {
            "question": "Explain why the sky appears blue during the day.",
            "context": "Science explanation",
        },
        "expected_output": "The sky appears blue due to a phenomenon called Rayleigh scattering. When sunlight enters Earth's atmosphere, it collides with tiny gas molecules. Blue light has a shorter wavelength than other colors, so it gets scattered more in all directions by these molecules, making the sky appear blue to our eyes.",
        "metadata": {
            "difficulty": "medium",
            "category": "science",
            "type": "explanation",
        },
    },
]

@router.post("/{staff_id}/test-prompt-experiment", response_model=PromptExperimentResponse, status_code=status.HTTP_200_OK, summary="Test prompt experiment with Langfuse", description="Run a prompt experiment on a Langfuse dataset using your chatbot and a real evaluation function.")
async def test_prompt_experiment(
    staff_id: str = Path(..., description="Unique identifier of the staff member"),
    request: PromptExperimentRequest = Body(..., description="Prompt experiment request")
):
    """
    Test prompt experiment using LangfusePromptService and your chatbot.
    """
    try:
        service = LangfusePromptService()
        from onexbots.shared.services.virtual_staff_service import get_virtual_staff

        staff_data = get_virtual_staff(staff_id)
        if not staff_data or not staff_data.get("data"):
            return PromptExperimentResponse(
                success=False, message=f"Staff member {staff_id} not found."
            )
        staff_config = staff_data["data"]
        from app.services.agent.staff_agent import StaffAgent
        staff_agent = StaffAgent(staff_config)

        class RealLLMApp:
            async def run(self, input):
                # Try to get 'message', fallback to 'question'
                if isinstance(input, dict):
                    message = input.get("message") or input.get("question")
                else:
                    message = input
                if not message:
                    raise ValueError("No message or question found in dataset input.")
                result = await staff_agent.process_chat(
                    message=message,
                    staff_id=staff_config["id"],
                    name="Experiment User",
                    phone_number="999999999",
                )
                return result["response"]

        def real_eval_fn(input, output, expected_output):
            return int(output.strip() == expected_output.strip())

        await service.run_prompt_experiment_async(
            dataset_name=request.dataset_name,
            run_name=request.run_name,
            my_llm_application=RealLLMApp(),
            my_eval_fn=real_eval_fn,
            run_description="Experiment with real chatbot",
            run_metadata={
                "model": staff_config["configuration"]["llm_settings"]["llm_model"]["model_name"]
            },
        )
        return PromptExperimentResponse(
            success=True,
            message="Prompt experiment run completed and logged to Langfuse.",
        )
    except Exception as e:
        return PromptExperimentResponse(success=False, message=f"Error: {str(e)}")

@router.post("/create-dataset", response_model=CreateDatasetResponse)
async def create_dataset(request: CreateDatasetRequest = Body(...)):
    """Create a new Langfuse dataset."""
    try:
        service = LangfusePromptService()
        service.create_dataset(request.dataset_name, request.description or "")
        return CreateDatasetResponse(
            success=True, message="Dataset created successfully."
        )
    except Exception as e:
        return CreateDatasetResponse(success=False, message=f"Error: {str(e)}")

@router.post("/add-dataset-items", response_model=AddDatasetItemsResponse)
async def add_dataset_items(request: AddDatasetItemsRequest = Body(...)):
    """Add items to a Langfuse dataset."""
    try:
        service = LangfusePromptService()
        service.add_dataset_items(request.dataset_name, dataset_items)
        return AddDatasetItemsResponse(
            success=True, message="Items added to dataset successfully."
        )
    except Exception as e:
        return AddDatasetItemsResponse(success=False, message=f"Error: {str(e)}")
