import os
from langfuse import Lang<PERSON>
from onexbots.shared.config import settings

from langfuse import get_client


class LangfusePromptService:
    def __init__(self):
        self.langfuse = get_client()

    def create_prompt(
        self,
        name: str,
        prompt,
        type: str = "text",
        description: str = None,
        labels: list = None,
        config: dict = None,
    ):
        """
        Create a new prompt in Langfuse.
        """
        return self.langfuse.create_prompt(
            name=name,
            type=type,
            prompt=prompt,
            description=description,
            labels=labels or [],
            config=config or {},
        )

    def update_prompt(
        self,
        name: str,
        prompt=None,
        type: str = None,
        description: str = None,
        labels: list = None,
        config: dict = None,
    ):
        """
        Update an existing prompt in Langfuse.
        """
        return self.langfuse.update_prompt(
            name=name,
            prompt=prompt,
            type=type,
            description=description,
            labels=labels,
            config=config,
        )

    # def delete_prompt(self, name: str):
    #     """
    #     Delete a prompt from Langfuse.
    #     """
    #     return self.langfuse.(name=name)

    def get_prompt(self, name: str):
        """
        Get a prompt from Langfuse.
        """
        return self.langfuse.get_prompt(name=name)

    def create_dataset(self, dataset_name: str, description: str = ""):
        """
        Create a new dataset in Langfuse.
        """
        return self.langfuse.create_dataset(name=dataset_name, description=description)

    def add_dataset_items(self, dataset_name: str, items: list):
        """
        Add items to a dataset in Langfuse.
        Each item should be a dict with 'input', 'expected_output', and optional 'metadata'.
        """
        for item in items:
            self.langfuse.create_dataset_item(
                dataset_name=dataset_name,
                input=item["input"],
                expected_output=item["expected_output"],
                metadata=item.get("metadata", {}),
            )

    def run_prompt_experiment(
        self,
        dataset_name: str,
        run_name: str,
        my_llm_application,
        my_eval_fn,
        run_description: str = "",
        run_metadata: dict = None,
        example_eval_name: str = "example_eval",
    ):
        """
        Run prompt experiments on a dataset, tracing and scoring each item.
        """
        dataset = self.langfuse.get_dataset(dataset_name)
        for item in dataset.items:
            with item.run(
                run_name=run_name,
                run_description=run_description,
                run_metadata=run_metadata or {},
            ) as run_ctx:
                # LLM call
                output = my_llm_application.run(item.input)
                # Log output as metadata if possible
                if hasattr(run_ctx, "add_metadata"):
                    run_ctx.add_metadata({"llm_output": output})
                # Scoring
                score = my_eval_fn(item.input, output, item.expected_output)
                if hasattr(run_ctx, "score_trace"):
                    run_ctx.score_trace(
                        name=example_eval_name, value=score, comment="Evaluation score"
                    )
                elif hasattr(run_ctx, "add_metadata"):
                    run_ctx.add_metadata({"example_eval_score": score})

    async def run_prompt_experiment_async(
        self,
        dataset_name: str,
        run_name: str,
        my_llm_application,
        my_eval_fn,
        run_description: str = "",
        run_metadata: dict = None,
        example_eval_name: str = "example_eval",
    ):
        """
        Async version: Run prompt experiments on a dataset, tracing and scoring each item with async LLM calls.
        """
        dataset = self.langfuse.get_dataset(dataset_name)
        for item in dataset.items:
            with item.run(
                run_name=run_name,
                run_description=run_description,
                run_metadata=run_metadata or {},
            ) as run_ctx:
                output = await my_llm_application.run(item.input)
                if hasattr(run_ctx, "add_metadata"):
                    run_ctx.add_metadata({"llm_output": output})
                score = my_eval_fn(item.input, output, item.expected_output)
                if hasattr(run_ctx, "score_trace"):
                    run_ctx.score_trace(
                        name=example_eval_name, value=score, comment="Evaluation score"
                    )
                elif hasattr(run_ctx, "add_metadata"):
                    run_ctx.add_metadata({"example_eval_score": score})

